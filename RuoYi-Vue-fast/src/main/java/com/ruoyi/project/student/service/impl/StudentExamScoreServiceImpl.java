package com.ruoyi.project.student.service.impl;

import java.util.List;
import javax.validation.Validator;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.framework.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.student.mapper.StudentExamScoreMapper;
import com.ruoyi.project.student.mapper.StudentInfoMapper;
import com.ruoyi.project.student.domain.StudentExamScore;
import com.ruoyi.project.student.domain.StudentExamScoreImport;
import com.ruoyi.project.student.domain.StudentInfo;
import com.ruoyi.project.student.service.IStudentExamScoreService;

/**
 * 学生考试成绩Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class StudentExamScoreServiceImpl extends BaseController implements IStudentExamScoreService 
{
    @Autowired
    private StudentExamScoreMapper studentExamScoreMapper;

    @Autowired
    private StudentInfoMapper studentInfoMapper;

    @Autowired
    protected Validator validator;

    /**
     * 查询学生考试成绩
     * 
     * @param scoreId 学生考试成绩主键
     * @return 学生考试成绩
     */
    @Override
    public StudentExamScore selectStudentExamScoreByScoreId(Long scoreId)
    {
        return studentExamScoreMapper.selectStudentExamScoreByScoreId(scoreId);
    }

    /**
     * 查询学生考试成绩列表
     * 
     * @param studentExamScore 学生考试成绩
     * @return 学生考试成绩
     */
    @Override
    public List<StudentExamScore> selectStudentExamScoreList(StudentExamScore studentExamScore)
    {
        return studentExamScoreMapper.selectStudentExamScoreList(studentExamScore);
    }

    /**
     * 根据学生ID查询考试成绩列表
     * 
     * @param studentId 学生ID
     * @return 学生考试成绩集合
     */
    @Override
    public List<StudentExamScore> selectStudentExamScoreListByStudentId(Long studentId)
    {
        return studentExamScoreMapper.selectStudentExamScoreListByStudentId(studentId);
    }

    /**
     * 新增学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    @Override
    public int insertStudentExamScore(StudentExamScore studentExamScore)
    {
        studentExamScore.setCreateBy(getUsername());
        studentExamScore.setCreateTime(DateUtils.getNowDate());
        return studentExamScoreMapper.insertStudentExamScore(studentExamScore);
    }

    /**
     * 修改学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    @Override
    public int updateStudentExamScore(StudentExamScore studentExamScore)
    {
        studentExamScore.setUpdateBy(getUsername());
        studentExamScore.setUpdateTime(DateUtils.getNowDate());
        return studentExamScoreMapper.updateStudentExamScore(studentExamScore);
    }

    /**
     * 批量删除学生考试成绩
     * 
     * @param scoreIds 需要删除的学生考试成绩主键
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByScoreIds(Long[] scoreIds)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByScoreIds(scoreIds);
    }

    /**
     * 删除学生考试成绩信息
     * 
     * @param scoreId 学生考试成绩主键
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByScoreId(Long scoreId)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByScoreId(scoreId);
    }

    /**
     * 根据学生ID删除考试成绩
     * 
     * @param studentId 学生ID
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByStudentId(Long studentId)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByStudentId(studentId);
    }

    /**
     * 根据学生ID批量删除考试成绩
     * 
     * @param studentIds 学生ID数组
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByStudentIds(Long[] studentIds)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByStudentIds(studentIds);
    }

    /**
     * 导入学生考试成绩数据
     *
     * @param scoreImportList 成绩导入数据列表
     * @param courseType 课程类型（0校内课程 1企业课程）
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importStudentExamScore(List<StudentExamScoreImport> scoreImportList, String courseType, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(scoreImportList) || scoreImportList.size() == 0)
        {
            throw new ServiceException("导入学生成绩数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String courseTypeName = "0".equals(courseType) ? "校内课程" : "企业课程";

        for (StudentExamScoreImport scoreImport : scoreImportList)
        {
            try
            {
                // 验证是否存在这个学生
                StudentInfo student = studentInfoMapper.checkStudentNumberUnique(scoreImport.getStudentNumber());
                if (StringUtils.isNull(student))
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、学号 " + scoreImport.getStudentNumber() + " 导入失败：学生不存在");
                }
                else
                {
                    // 验证导入数据
                    BeanValidators.validateWithException(validator, scoreImport);

                    // 创建成绩对象
                    StudentExamScore examScore = new StudentExamScore();
                    examScore.setStudentId(student.getStudentId());
                    examScore.setCourseName(scoreImport.getCourseName());
                    examScore.setCourseType(courseType);
                    examScore.setExamType(scoreImport.getExamType());
                    examScore.setScore(scoreImport.getScore());
                    examScore.setCreateBy(operName);
                    examScore.setCreateTime(DateUtils.getNowDate());

                    // 检查是否已存在相同的成绩记录（同一学生、同一课程、同一考试类型）
                    StudentExamScore existingScore = findExistingScore(student.getStudentId(),
                        scoreImport.getCourseName(), courseType, scoreImport.getExamType());

                    if (StringUtils.isNull(existingScore))
                    {
                        // 新增成绩
                        this.insertStudentExamScore(examScore);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、学号 " + scoreImport.getStudentNumber() +
                            " 课程《" + scoreImport.getCourseName() + "》" + courseTypeName + "成绩导入成功");
                    }
                    else if (isUpdateSupport)
                    {
                        // 更新成绩
                        examScore.setScoreId(existingScore.getScoreId());
                        examScore.setUpdateBy(operName);
                        examScore.setUpdateTime(DateUtils.getNowDate());
                        this.updateStudentExamScore(examScore);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、学号 " + scoreImport.getStudentNumber() +
                            " 课程《" + scoreImport.getCourseName() + "》" + courseTypeName + "成绩更新成功");
                    }
                    else
                    {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、学号 " + scoreImport.getStudentNumber() +
                            " 课程《" + scoreImport.getCourseName() + "》导入失败：" + courseTypeName + "成绩已存在");
                    }
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、学号 " + scoreImport.getStudentNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 查找已存在的成绩记录
     */
    private StudentExamScore findExistingScore(Long studentId, String courseName, String courseType, String examType)
    {
        StudentExamScore queryScore = new StudentExamScore();
        queryScore.setStudentId(studentId);
        queryScore.setCourseName(courseName);
        queryScore.setCourseType(courseType);
        queryScore.setExamType(examType);

        List<StudentExamScore> existingScores = studentExamScoreMapper.selectStudentExamScoreList(queryScore);
        return existingScores.isEmpty() ? null : existingScores.get(0);
    }
}